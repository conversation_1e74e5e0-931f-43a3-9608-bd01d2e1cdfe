import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from examples.comm_opcua import OPCUAClient


class TestCallMethod:
    """Test the call_method implementation."""

    @pytest.fixture
    async def client(self):
        """Create a test client."""
        client = OPCUAClient('opc.tcp://localhost:4840/', None)
        client._client = AsyncMock()
        client._is_connected = asyncio.Event()
        client._is_connected.set()  # Simulate connected state
        return client

    @pytest.fixture
    async def client_with_namespace(self):
        """Create a test client with namespace."""
        client = OPCUAClient('opc.tcp://localhost:4840/', 'test_namespace')
        client._client = AsyncMock()
        client._is_connected = asyncio.Event()
        client._is_connected.set()  # Simulate connected state
        client._ns_index = 2
        return client

    @pytest.mark.asyncio
    async def test_call_method_without_namespace(self, client):
        """Test calling a method without namespace."""
        # Mock the node and its call_method
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(return_value="method_result")
        client._client.get_node.return_value = mock_node

        # Call the method
        result = await client.call_method("ns=0;i=123", "TestMethod", "arg1", 42)

        # Verify the result
        assert result == "method_result"
        client._client.get_node.assert_called_once_with("ns=0;i=123")
        mock_node.call_method.assert_called_once_with("TestMethod", "arg1", 42)

    @pytest.mark.asyncio
    async def test_call_method_with_namespace(self, client_with_namespace):
        """Test calling a method with namespace."""
        # Mock the all_nodes cache
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(return_value="namespace_method_result")
        client_with_namespace._all_nodes = {"2:TestNode": mock_node}

        # Call the method
        result = await client_with_namespace.call_method("TestNode", "TestMethod", "arg1", 42)

        # Verify the result
        assert result == "namespace_method_result"
        mock_node.call_method.assert_called_once_with("TestMethod", "arg1", 42)

    @pytest.mark.asyncio
    async def test_call_method_node_not_found(self, client_with_namespace):
        """Test calling a method when node is not found."""
        # Mock empty all_nodes cache
        client_with_namespace._all_nodes = {}

        # Call the method and expect ValueError
        with pytest.raises(ValueError, match="Node with name 'NonExistentNode' not found"):
            await client_with_namespace.call_method("NonExistentNode", "TestMethod")

    @pytest.mark.asyncio
    async def test_call_method_connection_error_retry(self, client):
        """Test that connection errors are retried."""
        # Mock the node and its call_method to fail first, then succeed
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(side_effect=[ConnectionError(), "retry_success"])
        client._client.get_node.return_value = mock_node

        # Mock asyncio.sleep to avoid actual delay in tests
        with patch('asyncio.sleep', new_callable=AsyncMock):
            result = await client.call_method("ns=0;i=123", "TestMethod")

        # Verify the result after retry
        assert result == "retry_success"
        assert mock_node.call_method.call_count == 2

    @pytest.mark.asyncio
    async def test_call_method_other_exception_propagated(self, client):
        """Test that non-connection errors are propagated."""
        # Mock the node and its call_method to raise a different exception
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(side_effect=RuntimeError("Test error"))
        client._client.get_node.return_value = mock_node

        # Call the method and expect the exception to be propagated
        with pytest.raises(RuntimeError, match="Test error"):
            await client.call_method("ns=0;i=123", "TestMethod")

    @pytest.mark.asyncio
    async def test_call_method_no_arguments(self, client):
        """Test calling a method with no arguments."""
        # Mock the node and its call_method
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(return_value="no_args_result")
        client._client.get_node.return_value = mock_node

        # Call the method with no arguments
        result = await client.call_method("ns=0;i=123", "TestMethod")

        # Verify the result
        assert result == "no_args_result"
        mock_node.call_method.assert_called_once_with("TestMethod")

    @pytest.mark.asyncio
    async def test_call_method_multiple_arguments(self, client):
        """Test calling a method with multiple arguments."""
        # Mock the node and its call_method
        mock_node = AsyncMock()
        mock_node.call_method = AsyncMock(return_value="multi_args_result")
        client._client.get_node.return_value = mock_node

        # Call the method with multiple arguments
        result = await client.call_method("ns=0;i=123", "TestMethod", 1, "string", True, [1, 2, 3])

        # Verify the result
        assert result == "multi_args_result"
        mock_node.call_method.assert_called_once_with("TestMethod", 1, "string", True, [1, 2, 3])
