"""
Example demonstrating how to use the call_method function.

This example shows how to call OPC UA methods on nodes using the OPCUAClient.
"""

import asyncio
from comm_opcua import OPCUAClient


async def main():
    """Demonstrate calling OPC UA methods."""
    # Create client - replace with your server URL
    client = OPCUAClient('opc.tcp://localhost:4840/', namespace_name=None)
    
    try:
        # Connect to the server
        await client.connect()
        print("Connected to OPC UA server")
        
        # Example 1: Call a method without arguments
        # Replace 'ns=2;i=1001' with your actual node ID
        # Replace 'StartProcess' with your actual method name
        try:
            result = await client.call_method('ns=2;i=1001', 'StartProcess')
            print(f"Method call result (no args): {result}")
        except Exception as e:
            print(f"Method call failed (no args): {e}")
        
        # Example 2: Call a method with arguments
        # Replace with your actual node ID and method name
        try:
            result = await client.call_method(
                'ns=2;i=1001', 
                'SetParameters', 
                100,  # First argument (e.g., speed)
                'AUTO',  # Second argument (e.g., mode)
                True  # Third argument (e.g., enable flag)
            )
            print(f"Method call result (with args): {result}")
        except Exception as e:
            print(f"Method call failed (with args): {e}")
        
        # Example 3: Call a method using namespace-based node lookup
        # This works when you specify a namespace_name in the client constructor
        client_with_ns = OPCUAClient('opc.tcp://localhost:4840/', namespace_name='MyNamespace')
        await client_with_ns.connect()
        
        try:
            result = await client_with_ns.call_method(
                'ProcessNode',  # Node name in the specified namespace
                'Reset'  # Method name
            )
            print(f"Namespace-based method call result: {result}")
        except Exception as e:
            print(f"Namespace-based method call failed: {e}")
        
        await client_with_ns._client.disconnect()
        
    except Exception as e:
        print(f"Connection or method call error: {e}")
    
    finally:
        # Disconnect from the server
        await client._client.disconnect()
        print("Disconnected from OPC UA server")


if __name__ == '__main__':
    # Run the example
    asyncio.run(main())
