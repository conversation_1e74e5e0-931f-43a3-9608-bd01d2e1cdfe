stages:
  - deploy

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"


deploy:
  stage: deploy
  image: python:3.10
  script:
    - python -m pip install --upgrade pip
    - python -m pip install twine build
    - python -m build
    - twine upload --repository-url $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/pypi dist/*
  variables:
    TWINE_PASSWORD: ${CI_JOB_TOKEN}
    TWINE_USERNAME: gitlab-ci-token
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /\[no-deploy\]/i'
      when: never
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
