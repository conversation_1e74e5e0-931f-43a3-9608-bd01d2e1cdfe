import asyncio
from inspect import isawaitable
from typing import Any

from asyncua import Node, Server
from asyncua.ua import VariantType


async def generate_nodes(
    root: Node,
    address_space: int,
    config: list[dict[str, Any]],
):
    res: dict[str, Node] = {}

    for entry in config:
        ids = entry['node_id']
        if isinstance(ids, str):
            ids = [ids]
        node_type = VariantType[entry['type']]
        default = entry['default']

        for node_id in ids:
            assert node_id not in res, f'Duplicate node id: {node_id}'
            node = await root.add_variable(address_space, node_id, default, varianttype=node_type)
            await node.set_writable()
            if 'array_dimensions' in entry:
                await node.write_array_dimensions(entry['array_dimensions'])
            res[node_id] = node

    return res


async def main():
    name = 'test server'
    url = 'opc.tcp://localhost:4840/'

    myserver = Server()
    await myserver.init()

    myserver.set_endpoint(url=url)
    address_space = await myserver.register_namespace(uri=name)

    root = myserver.get_objects_node() 

    nodes = await generate_nodes(root, address_space, [{'node_id': ['ns=2;i=1'], 'type': 'Boolean', 'default': False}, {'node_id': ['ns=2;i=2'], 'type': 'UInt16', 'default': 0}, {'node_id': ['ns=2;i=3'], 'type': 'String', 'default': 'Hello World'}])
    locals().update(nodes)

    async with myserver:
        while True:
            try:
                res = eval(await asyncio.get_running_loop().run_in_executor(None, input, '> '))

                if isawaitable(res):
                    res = await res
                if res is not None:
                    print(res)
            except Exception:
                # Print traceback
                import sys
                import traceback

                traceback.print_exc(file=sys.stdout)


# Run the main script
if __name__ == '__main__':
    asyncio.run(main())
