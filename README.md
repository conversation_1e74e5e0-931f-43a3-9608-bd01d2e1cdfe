# Python - OPC UA Package

Communicate with a PLC (or other OPC UA server) to read its nodes.


## Installation

To install this package, ensure `hv-pypi` is added to your pipfile. (see [wiki](https://git.heliovision.be/heliovision/heliovision-wiki/-/blob/main/Manuals/Installing%20python%20packages%20from%20our%20package%20registry.md?ref_type=heads))  
Then run command:
```
pipenv install heliovision.communication.opcua --index=hv-pypi
```

To update the package:
```
pipenv update heliovision.communication.opcua --index=hv-pypi
```
