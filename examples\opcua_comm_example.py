import asyncio
from dataclasses import dataclass
from datetime import datetime
from pprint import pprint
from typing import TypedDict

from comm_opcua import OPCUAClient, OPCUANode


async def heartbeat_in(heartbeat_node: OPCUANode[bool]):
    global last_beat  # This will be a bit more local in your code hopefully
    last_beat = datetime.fromordinal(1)
    while True:
        await heartbeat_node.wait_for_value(True)
        last_beat = datetime.now()
        await heartbeat_node.wait_for_value(False)
        last_beat = datetime.now()


async def heartbeat_out(heartbeat_node: OPCUANode[bool]):
    while True:
        await asyncio.sleep(1)
        await heartbeat_node.set(True)
        await asyncio.sleep(1)
        await heartbeat_node.set(False)


# Custom dataclass: you can make any dataclass and read it in one go as long as the struct on server side matches this.
@dataclass(frozen=True)
class Pose:
    """Robot Pose"""

    x: float
    y: float
    z: float
    rx: float
    ry: float
    rz: float


@dataclass(frozen=True)
class PalletPose:
    """Robot Pose"""

    rz: float
    x: float
    y: float
    z: float


class Nodes(TypedDict):
    """TypedDict for the nodes. Make sure the name matches that on server side."""

    pose_drop: OPCUANode[PalletPose]
    # x: OPCUANode[int]
    # y: OPCUANode[int]
    # z: OPCUANode[int]
    # rz: OPCUANode[int]


async def main():
    client = OPCUAClient('opc.tcp://192.168.255.1:16448/', 5)
    await client.connect()
    try:
        # all_nodes = await client.get_all_nodes()
        # pprint(all_nodes)
        # Build the nodes dictionary by calling client.get_node for each node
        nodes: Nodes = {}  # type: ignore
        for node_name, node_type in Nodes.__annotations__.items():
            nodes[node_name] = await client.get_node(node_name, node_type)
            print(f'Connected to {node_name}')

        # # Start the heartbeat tasks
        # heartbeat_tasks = [  # noqa: F841
        #     asyncio.create_task(heartbeat_in(nodes['Heartbeat_PLC_Running'])),
        #     asyncio.create_task(heartbeat_out(nodes['Heartbeat_Python_Running'])),
        # ]

        # # Async wait for a value
        # await nodes['Some_Flag'].wait_for_value(True)

        # # Set and get values
        # await nodes['Some_Counter'].set(await nodes['Some_Counter'].get() + 1)
        # await nodes['Some_Measurement'].set(3.14)
        # await nodes['Some_String'].set('Hello, World!')
        # assert await nodes['Some_String'].get() == 'Hello, World!'
        # await nodes['Some_List'].set([1, 2, 3, 4, 5])

        # # Read the robot pose
        # robot_pose = await nodes['Robot_Pose'].get()  # noqa: F841
        # # Write the robot pose
        # await nodes['Robot_Pose'].set(Pose(1, 2, 3, 4, 5, 6))
    finally:
        await client._client.disconnect()


if __name__ == '__main__':
    asyncio.run(main())
