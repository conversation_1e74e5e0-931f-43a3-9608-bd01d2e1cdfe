[build-system]

requires = ["setuptools >= 61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]

name = "heliovision.communication.opcua"
version = "0.4.1"
description = "Package to connect and establish direct communication between Python and OPC UA servers"

dependencies = [
    "asyncua >= 1.1.5, ==1.*",
    "loguru >= 0.7.2",
    "typing_extensions >= 4.12.2, ==4.*",
]

authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "Luwe", email = "<EMAIL>" },
]

keywords = ["opcua", "opc/ua", "opc ua"]

[tool.setuptools]
packages = ["heliovision.communication.opcua"]

[tool.pytest.ini_options]
addopts = ["--import-mode=importlib"]

[tool.ruff]
line-length = 120

lint.extend-select = ["I"]  # Fix import order

[tool.ruff.format]
quote-style = "single"
